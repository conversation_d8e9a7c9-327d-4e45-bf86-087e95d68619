/**
 * Core Type Definitions for Kritrima AI CLI
 */

import type { OpenAI } from 'openai';

// ============================================================================
// Configuration Types
// ============================================================================

export interface AppConfig {
  model: string;
  provider: string;
  approvalMode: ApprovalPolicy;
  providers?: Record<string, ProviderConfig>;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  debug?: boolean;
  notifications?: boolean;
  saveHistory?: boolean;
  maxHistorySize?: number;
  workingDirectory?: string;
  additionalWritableRoots?: string[];
  projectDocPath?: string;
  disableResponseStorage?: boolean;
}

export interface ProviderConfig {
  name: string;
  baseURL: string;
  envKey: string;
  models?: string[];
  defaultModel?: string;
  supportsImages?: boolean;
  supportsTools?: boolean;
  maxContextLength?: number;
}

export type ApprovalPolicy = 'suggest' | 'auto' | 'auto-edit' | 'full-auto' | 'manual';

// ============================================================================
// Response and Message Types
// ============================================================================

export interface ResponseItem {
  id: string;
  type: 'message' | 'function_call' | 'function_result' | 'error' | 'system' | 'command_output';
  timestamp: number;
  content: ResponseContent[];
  metadata?: Record<string, any>;
  // Command execution properties
  command?: string[];
  output?: string;
  exit_code?: number;
  duration?: number;
  error?: string | Error;
  function_name?: string;
  arguments?: any;
}

export interface ResponseInputItem {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: ResponseContentInput[];
  type: 'message';
  id?: string;
  timestamp?: number;
}

export interface ResponseOutputItem {
  role: 'assistant';
  content: ResponseContentOutput[];
  type: 'message' | 'command_output';
  id: string;
  timestamp: number;
  model?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  // Command execution properties
  command?: string[];
  output?: string;
  exit_code?: number;
  duration?: number;
  error?: string | Error;
  function_name?: string;
  arguments?: any;
}

export type ResponseContent = ResponseContentInput | ResponseContentOutput;

export interface ResponseContentInput {
  type: 'input_text' | 'input_image';
  text?: string;
  image?: {
    url?: string;
    data?: string;
    mimeType?: string;
  };
}

export interface ResponseContentOutput {
  type: 'output_text' | 'function_call' | 'function_result';
  text?: string;
  functionCall?: ResponseFunctionToolCall;
  functionResult?: {
    name: string;
    result: string;
    success: boolean;
    metadata?: Record<string, any>;
  };
}

export interface ResponseFunctionToolCall {
  id: string;
  name: string;
  arguments: string;
}

// ============================================================================
// Tool and Function Types
// ============================================================================

export interface FunctionTool {
  type: 'function';
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
}

export interface ExecInput {
  command: string[];
  workdir?: string;
  timeout?: number;
  env?: Record<string, string>;
}

export interface ExecResult {
  success?: boolean;
  exitCode: number;
  stdout: string;
  stderr: string;
  command?: string[];
  workdir?: string;
  duration: number;
  timedOut?: boolean;
  signal?: string;
  metadata?: Record<string, any>;
}

// ============================================================================
// Agent and Loop Types
// ============================================================================

export interface AgentLoopConfig {
  model: string;
  provider: string;
  approvalPolicy: ApprovalPolicy;
  maxIterations?: number;
  timeout?: number;
  debug?: boolean;
}

export interface AgentState {
  sessionId: string;
  conversationId: string;
  items: ResponseItem[];
  currentModel: string;
  currentProvider: string;
  approvalPolicy: ApprovalPolicy;
  metadata: Record<string, any>;
}

// ============================================================================
// UI and Component Types
// ============================================================================

export type OverlayModeType = 
  | 'none' 
  | 'history' 
  | 'sessions' 
  | 'model' 
  | 'approval' 
  | 'help' 
  | 'diff';

export interface TerminalSize {
  columns: number;
  rows: number;
}

export interface KeyEvent {
  name: string;
  ctrl: boolean;
  shift: boolean;
  alt: boolean;
  meta: boolean;
  sequence: string;
}

// ============================================================================
// Storage and Session Types
// ============================================================================

export interface SessionData {
  id: string;
  timestamp: number;
  items: ResponseItem[];
  config: Partial<AppConfig>;
  metadata: {
    model: string;
    provider: string;
    duration: number;
    messageCount: number;
    tokenUsage?: {
      prompt: number;
      completion: number;
      total: number;
    };
  };
}

export interface HistoryEntry {
  command: string;
  timestamp: number;
  sessionId?: string;
  success?: boolean;
}

export interface HistoryConfig {
  maxSize: number;
  saveHistory: boolean;
  sensitivePatterns: (string | RegExp)[];
}

// ============================================================================
// Error and Event Types
// ============================================================================

export interface KritrimaError extends Error {
  code: string;
  details?: Record<string, any>;
  recoverable?: boolean;
}

export interface ResponseEvent {
  type: string;
  data?: any;
  timestamp: number;
}

// ============================================================================
// File and Patch Types
// ============================================================================

export interface FileContent {
  path: string;
  content: string;
  size: number;
  mimeType?: string;
}

export interface PatchOperation {
  type: 'create' | 'update' | 'delete' | 'move';
  path: string;
  content?: string;
  newPath?: string;
  backup?: string;
}

export interface DiffResult {
  files: Array<{
    path: string;
    status: 'added' | 'modified' | 'deleted' | 'renamed';
    additions: number;
    deletions: number;
    diff: string;
  }>;
  summary: {
    totalFiles: number;
    totalAdditions: number;
    totalDeletions: number;
  };
}

// ============================================================================
// Utility Types
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
